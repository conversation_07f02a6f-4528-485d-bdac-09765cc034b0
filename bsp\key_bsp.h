#ifndef __KEY_BSP_H__
#define __KEY_BSP_H__

#include "bsp_system.h"
void key_proc(void);
uint8_t key_read(void);

void rotate_to_target_angles(void);
void reset_to_initial_position(void);

// External variable declarations
extern float target_x_angle;
extern float target_y_angle;

// External variables from uart_bsp.c
extern uint8_t initial_position_saved;
extern uint32_t x_initial_position;
extern uint32_t y_initial_position;
extern uint8_t x_initial_direction;
extern uint8_t y_initial_direction;

// External UART and motor definitions
extern UART_HandleTypeDef huart1;  // Debug UART
extern UART_HandleTypeDef MOTOR_X_UART;
extern UART_HandleTypeDef MOTOR_Y_UART;


#endif
