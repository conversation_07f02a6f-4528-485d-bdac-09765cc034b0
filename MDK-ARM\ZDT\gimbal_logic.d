zdt\gimbal_logic.o: ..\bsp\gimbal_logic.c
zdt\gimbal_logic.o: ..\bsp\gimbal_logic.h
zdt\gimbal_logic.o: ..\bsp\bsp_system.h
zdt\gimbal_logic.o: C:\keil5\core\ARM\ARM_Compiler_5.06u7\Bin\..\include\stdarg.h
zdt\gimbal_logic.o: C:\keil5\core\ARM\ARM_Compiler_5.06u7\Bin\..\include\string.h
zdt\gimbal_logic.o: C:\keil5\core\ARM\ARM_Compiler_5.06u7\Bin\..\include\stdbool.h
zdt\gimbal_logic.o: C:\keil5\core\ARM\ARM_Compiler_5.06u7\Bin\..\include\stdlib.h
zdt\gimbal_logic.o: C:\keil5\core\ARM\ARM_Compiler_5.06u7\Bin\..\include\stdint.h
zdt\gimbal_logic.o: C:\keil5\core\ARM\ARM_Compiler_5.06u7\Bin\..\include\stdio.h
zdt\gimbal_logic.o: C:\keil5\core\ARM\ARM_Compiler_5.06u7\Bin\..\include\math.h
zdt\gimbal_logic.o: ../Core/Inc/main.h
zdt\gimbal_logic.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal.h
zdt\gimbal_logic.o: ../Core/Inc/stm32f4xx_hal_conf.h
zdt\gimbal_logic.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_rcc.h
zdt\gimbal_logic.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_def.h
zdt\gimbal_logic.o: ../Drivers/CMSIS/Device/ST/STM32F4xx/Include/stm32f4xx.h
zdt\gimbal_logic.o: ../Drivers/CMSIS/Device/ST/STM32F4xx/Include/stm32f407xx.h
zdt\gimbal_logic.o: ../Drivers/CMSIS/Include/core_cm4.h
zdt\gimbal_logic.o: ../Drivers/CMSIS/Include/cmsis_version.h
zdt\gimbal_logic.o: ../Drivers/CMSIS/Include/cmsis_compiler.h
zdt\gimbal_logic.o: ../Drivers/CMSIS/Include/cmsis_armcc.h
zdt\gimbal_logic.o: ../Drivers/CMSIS/Include/mpu_armv7.h
zdt\gimbal_logic.o: ../Drivers/CMSIS/Device/ST/STM32F4xx/Include/system_stm32f4xx.h
zdt\gimbal_logic.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal.h
zdt\gimbal_logic.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h
zdt\gimbal_logic.o: C:\keil5\core\ARM\ARM_Compiler_5.06u7\Bin\..\include\stddef.h
zdt\gimbal_logic.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_rcc_ex.h
zdt\gimbal_logic.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_gpio.h
zdt\gimbal_logic.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_gpio_ex.h
zdt\gimbal_logic.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_exti.h
zdt\gimbal_logic.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_dma.h
zdt\gimbal_logic.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_dma_ex.h
zdt\gimbal_logic.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_cortex.h
zdt\gimbal_logic.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_flash.h
zdt\gimbal_logic.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_flash_ex.h
zdt\gimbal_logic.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_flash_ramfunc.h
zdt\gimbal_logic.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_pwr.h
zdt\gimbal_logic.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_pwr_ex.h
zdt\gimbal_logic.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_tim.h
zdt\gimbal_logic.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_tim_ex.h
zdt\gimbal_logic.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_uart.h
zdt\gimbal_logic.o: ../Core/Inc/dma.h
zdt\gimbal_logic.o: ../Core/Inc/usart.h
zdt\gimbal_logic.o: ../Core/Inc/tim.h
zdt\gimbal_logic.o: ..\bsp\schedule.h
zdt\gimbal_logic.o: ..\bsp\bsp_system.h
zdt\gimbal_logic.o: ../ringbuffer/ringbuffer.h
zdt\gimbal_logic.o: C:\keil5\core\ARM\ARM_Compiler_5.06u7\Bin\..\include\assert.h
zdt\gimbal_logic.o: ..\bsp\key_bsp.h
zdt\gimbal_logic.o: ..\bsp\encoder_bsp.h
zdt\gimbal_logic.o: ..\bsp\motor_bsp.h
zdt\gimbal_logic.o: ..\bsp\uart_bsp.h
zdt\gimbal_logic.o: ..\bsp\step_motor_bsp.h
zdt\gimbal_logic.o: ..\bsp\gray_bsp.h
zdt\gimbal_logic.o: ..\bsp\pi_bsp.h
zdt\gimbal_logic.o: ..\bsp\gimbal_logic.h
zdt\gimbal_logic.o: ..\bsp\config.h
zdt\gimbal_logic.o: ../app/encoder_drv.h
zdt\gimbal_logic.o: ../app/Emm_V5.h
zdt\gimbal_logic.o: ../app/mypid.h
