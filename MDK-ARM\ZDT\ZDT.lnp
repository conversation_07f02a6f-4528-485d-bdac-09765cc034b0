--cpu=Cortex-M4.fp.sp
"zdt\startup_stm32f407xx.o"
"zdt\main.o"
"zdt\gpio.o"
"zdt\dma.o"
"zdt\tim.o"
"zdt\usart.o"
"zdt\stm32f4xx_it.o"
"zdt\stm32f4xx_hal_msp.o"
"zdt\stm32f4xx_hal_tim.o"
"zdt\stm32f4xx_hal_tim_ex.o"
"zdt\stm32f4xx_hal_rcc.o"
"zdt\stm32f4xx_hal_rcc_ex.o"
"zdt\stm32f4xx_hal_flash.o"
"zdt\stm32f4xx_hal_flash_ex.o"
"zdt\stm32f4xx_hal_flash_ramfunc.o"
"zdt\stm32f4xx_hal_gpio.o"
"zdt\stm32f4xx_hal_dma_ex.o"
"zdt\stm32f4xx_hal_dma.o"
"zdt\stm32f4xx_hal_pwr.o"
"zdt\stm32f4xx_hal_pwr_ex.o"
"zdt\stm32f4xx_hal_cortex.o"
"zdt\stm32f4xx_hal.o"
"zdt\stm32f4xx_hal_exti.o"
"zdt\stm32f4xx_hal_uart.o"
"zdt\system_stm32f4xx.o"
"zdt\ringbuffer.o"
"zdt\emm_v5.o"
"zdt\encoder_drv.o"
"zdt\motor_driver.o"
"zdt\mypid.o"
"zdt\encoder_bsp.o"
"zdt\pi_bsp.o"
"zdt\schedule.o"
"zdt\step_motor_bsp.o"
"zdt\uart_bsp.o"
"zdt\key_bsp.o"
"zdt\gimbal_logic.o"
--strict --scatter "ZDT\ZDT.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "ZDT.map" -o ZDT\ZDT.axf