#ifndef __CONFIG_H__
#define __CONFIG_H__

// 云台运动参数配置
#define GIMBAL_MODE1_X_PULSE    100    // 第一问X轴脉冲数（需根据实际测量调整）
#define GIMBAL_MODE1_Y_PULSE    100     // 第一问Y轴脉冲数（需根据实际测量调整）
#define GIMBAL_MODE1_SPEED      30      // 第一问运动速度(RPM)
#define GIMBAL_MODE1_ACCEL      10      // 第一问加速度

#define GIMBAL_MODE2_X_PULSE    1500    // 第二问X轴脉冲数（预留）
#define GIMBAL_MODE2_Y_PULSE    1200    // 第二问Y轴脉冲数（预留）
#define GIMBAL_MODE2_SPEED      25      // 第二问运动速度(RPM)
#define GIMBAL_MODE2_ACCEL      8       // 第二问加速度

// 系统时间配置
#define POWER_ON_DELAY_MS       500     // 上电延时(ms)
#define MOTION_TIMEOUT_MS       10000   // 运动超时时间(ms)
#define KEY_CHECK_PERIOD_MS     10      // 按键检查周期(ms)

// 调试输出配置
#define DEBUG_UART              huart1  // 调试串口
#define DEBUG_ENABLE            1       // 是否使能调试输出

#endif
