#include "key_bsp.h"
#include "uart_bsp.h"
#include "step_motor_bsp.h"
#include "Emm_V5.h"  // 用于 Emm_V5_Pos_Control 和其他电机控制功能
uint8_t key_val = 0;
uint8_t key_old = 0;
uint8_t key_down = 0;
uint8_t key_up = 0;

// 为X和Y电机定义目标角度
float target_x_angle = 30.0f; // Example value - replace with your measured value
float target_y_angle = 45.0f; // Example value - replace with your measured value

uint8_t key_read(void)
{
    uint8_t temp = 0;
    
    if(HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_3) == GPIO_PIN_RESET)
        temp = 1;
    if(HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_4) == GPIO_PIN_RESET)
        temp = 2;
//    if(HAL_GPIO_ReadPin(KEY3_GPIO_Port, KEY3_Pin) == GPIO_PIN_RESET)
//        temp = 3;
//    if(HAL_GPIO_ReadPin(KEY4_GPIO_Port, KEY4_Pin) == GPIO_PIN_RESET)
//        temp = 4;
    return temp;
}

/**
 * @brief 旋转电机到目标角度的功能
 */
void rotate_to_target_angles(void)
{
    //仅在初始位置已保存的情况下继续
//    if (initial_position_saved)
    {
        my_printf(&huart1, "Rotating motors to target angles: X=%.1f°, Y=%.1f°\r\n", 
                 target_x_angle, target_y_angle);
        
        // 根据角度计算目标位置
        // 65536个脉冲 = 360度，因此我们将角度转换为脉冲
        uint32_t x_target_pulses = (uint32_t)(x_initial_position + (target_x_angle * 65536.0f / 360.0f));
        uint32_t y_target_pulses = (uint32_t)(y_initial_position + (target_y_angle * 65536.0f / 360.0f));
        
        // 根据最短路径确定方向
        uint8_t x_dir = (target_x_angle >= 0) ? 0 : 1;
        uint8_t y_dir = (target_y_angle >= 0) ? 0 : 1;
        
        // 将X电机移动到目标位置
        Emm_V5_Pos_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_dir, 
                          MOTOR_MAX_SPEED/2, MOTOR_ACCEL, x_target_pulses, 
                          true, MOTOR_SYNC_FLAG);
        
        // 将Y电机移动到目标位置
        Emm_V5_Pos_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, y_dir, 
                          MOTOR_MAX_SPEED/2, MOTOR_ACCEL, y_target_pulses, 
                          true, MOTOR_SYNC_FLAG);
    }
//    else
//    {
//        my_printf(&huart1, "Error: Initial positions not saved yet\r\n");
//    }
}

/**
 * @brief 将电机返回到初始位置
 */
void reset_to_initial_position(void)
{
    //仅在初始位置已保存的情况下继续
    if (initial_position_saved)
    {
        my_printf(&huart1, "Resetting motors to initial position\r\n");
        
        // 使用绝对位置模式返回初始位置
        Emm_V5_Pos_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_initial_direction, 
                          MOTOR_MAX_SPEED/2, MOTOR_ACCEL, x_initial_position, 
                          true, MOTOR_SYNC_FLAG);
        
        Emm_V5_Pos_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, y_initial_direction, 
                          MOTOR_MAX_SPEED/2, MOTOR_ACCEL, y_initial_position, 
                          true, MOTOR_SYNC_FLAG);
    }
    else
    {
        my_printf(&huart1, "Error: Initial positions not saved yet\r\n");
    }
}

void key_proc(void)
{
    key_val = key_read();
    key_down = key_val & (key_val ^ key_old);
    key_up = ~key_val & (key_val ^ key_old);
    key_old = key_val;
    
    // 按钮 1 被按下 - 旋转到目标角度
    if(key_down == 1)
    {
        my_printf(&huart1, "Button 1 pressed - Rotating to target angles\r\n");
        rotate_to_target_angles();
    }
    
    // 按钮 2 被按下 - 重置到初始位置
    if(key_down == 2)
    {
        my_printf(&huart1, "Button 2 pressed - Resetting to initial position\r\n");
        Step_Motor_Stop();
    }
    
    // 按钮3被按下 - 立即停止电机
    if(key_down == 3)
    {
        my_printf(&huart1, "Button 3 pressed - Stopping motors\r\n");
        Step_Motor_Stop();
    }
    
    // 如果需要，按钮4可以用于其他功能
    if(key_down == 4)
    {
        my_printf(&huart1, "Button 4 pressed\r\n");
        // 如有需要，添加额外功能
    }
}
